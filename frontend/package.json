{"name": "jsmith-author-frontend", "version": "1.0.0", "private": true, "type": "module", "scripts": {"dev": "src/node_modules/.bin/vite", "build": "src/node_modules/.bin/tsc && vite build", "preview": "src/node_modules/.bin/vite preview", "lint": "src/node_modules/.bin/eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "test": "src/node_modules/.bin/vitest", "test:ui": "src/node_modules/.bin/vitest --ui", "test:coverage": "src/node_modules/.bin/vitest --coverage"}, "dependencies": {"@hookform/resolvers": "^3.9.0", "@tanstack/react-query": "^5.56.2", "axios": "^1.7.7", "clsx": "^2.1.1", "date-fns": "^3.6.0", "framer-motion": "^11.5.4", "react": "^18.3.1", "react-dom": "^18.3.1", "react-helmet-async": "^2.0.5", "react-hook-form": "^7.53.0", "react-hot-toast": "^2.4.1", "react-intersection-observer": "^9.13.1", "react-markdown": "^9.0.1", "react-router-dom": "^6.26.2", "react-syntax-highlighter": "^15.5.0", "rehype-raw": "^7.0.0", "remark-gfm": "^4.0.0", "tailwind-merge": "^2.5.2", "zod": "^3.23.8", "zustand": "^4.5.5"}, "devDependencies": {"@tailwindcss/forms": "^0.5.9", "@tailwindcss/typography": "^0.5.15", "@types/node": "^22.5.5", "@types/react": "^18.3.8", "@types/react-dom": "^18.3.0", "@types/react-syntax-highlighter": "^15.5.13", "@typescript-eslint/eslint-plugin": "^8.6.0", "@typescript-eslint/parser": "^8.6.0", "@vitejs/plugin-react": "^4.3.1", "autoprefixer": "^10.4.20", "eslint": "^8.57.1", "eslint-plugin-react-hooks": "^4.6.2", "eslint-plugin-react-refresh": "^0.4.12", "postcss": "^8.4.47", "tailwindcss": "^3.4.12", "typescript": "^5.6.2", "vite": "^5.4.7", "vite-plugin-compression": "^0.5.1", "vite-plugin-pwa": "^0.20.5", "vitest": "^2.1.1"}}