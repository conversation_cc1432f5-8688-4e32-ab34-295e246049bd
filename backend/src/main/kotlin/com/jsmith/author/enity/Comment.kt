// entity/Comment.kt
package com.jsmith.author.entity

import jakarta.persistence.*

@Entity
@Table(name = "comments", indexes = [
    Index(name = "idx_comment_user", columnList = "user_id"),
    Index(name = "idx_comment_novel", columnList = "novel_id"),
    Index(name = "idx_comment_blog", columnList = "blog_id"),
    Index(name = "idx_comment_story", columnList = "story_id")
])
class Comment(
    @Column(columnDefinition = "TEXT", nullable = false)
    var content: String,
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "user_id", nullable = false)
    var user: User,
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "novel_id")
    var novel: Novel? = null,
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "blog_id")
    var blog: Blog? = null,
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "story_id")
    var story: Story? = null,
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "parent_id")
    var parent: Comment? = null,
    
    @OneToMany(mappedBy = "parent", cascade = [CascadeType.ALL])
    @OrderBy("createdAt ASC")
    var replies: MutableSet<Comment> = mutableSetOf(),
    
    @Column(nullable = false)
    var isApproved: Boolean = true,
    
    @Column(nullable = false)
    var isDeleted: Boolean = false,
    
    @Column
    var editedAt: LocalDateTime? = null
) : BaseEntity()