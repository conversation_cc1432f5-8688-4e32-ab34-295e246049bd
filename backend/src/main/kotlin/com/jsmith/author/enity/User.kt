// entity/User.kt
package com.jsmith.author.entity

import jakarta.persistence.*
import org.springframework.security.core.GrantedAuthority
import org.springframework.security.core.authority.SimpleGrantedAuthority
import org.springframework.security.core.userdetails.UserDetails

@Entity
@Table(name = "users", indexes = [
    Index(name = "idx_user_email", columnList = "email", unique = true),
    Index(name = "idx_user_username", columnList = "username", unique = true)
])
class User(
    @Column(unique = true, nullable = false, length = 100)
    var email: String,
    
    @Column(unique = true, nullable = false, length = 50)
    private var username: String,
    
    @Column(nullable = false)
    private var password: String,
    
    @Column(nullable = false, length = 100)
    var firstName: String,
    
    @Column(nullable = false, length = 100)
    var lastName: String,
    
    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    var role: UserRole = UserRole.USER,
    
    @Column(nullable = false)
    private var enabled: Boolean = false,
    
    @Column(nullable = false)
    var emailVerified: Boolean = false,
    
    @Column
    var emailVerificationToken: String? = null,
    
    @Column
    var passwordResetToken: String? = null,
    
    @Column
    var passwordResetTokenExpiry: LocalDateTime? = null,
    
    @Column
    var lastLoginAt: LocalDateTime? = null,
    
    @Column
    var failedLoginAttempts: Int = 0,
    
    @Column
    var accountLockedUntil: LocalDateTime? = null,
    
    @OneToMany(mappedBy = "user", cascade = [CascadeType.ALL])
    var comments: MutableSet<Comment> = mutableSetOf(),
    
    @OneToMany(mappedBy = "user", cascade = [CascadeType.ALL])
    var subscriptions: MutableSet<Subscription> = mutableSetOf()
) : BaseEntity(), UserDetails {
    
    override fun getAuthorities(): Collection<GrantedAuthority> {
        return listOf(SimpleGrantedAuthority("ROLE_${role.name}"))
    }
    
    override fun getPassword() = password
    override fun getUsername() = username
    override fun isAccountNonExpired() = true
    override fun isAccountNonLocked() = accountLockedUntil?.isBefore(LocalDateTime.now()) ?: true
    override fun isCredentialsNonExpired() = true
    override fun isEnabled() = enabled && emailVerified
    
    fun setPassword(password: String) {
        this.password = password
    }
    
    fun setUsername(username: String) {
        this.username = username
    }
    
    fun setEnabled(enabled: Boolean) {
        this.enabled = enabled
    }
}

enum class UserRole {
    USER, MODERATOR, ADMIN
}
