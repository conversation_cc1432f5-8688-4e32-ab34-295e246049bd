// service/UserService.kt
package com.jsmith.author.service

import com.jsmith.author.dto.*
import com.jsmith.author.entity.User
import com.jsmith.author.entity.UserRole
import com.jsmith.author.exception.EmailAlreadyExistsException
import com.jsmith.author.exception.InvalidTokenException
import com.jsmith.author.exception.ResourceNotFoundException
import com.jsmith.author.repository.UserRepository
import com.jsmith.author.security.JwtService
import org.springframework.cache.annotation.CacheEvict
import org.springframework.cache.annotation.Cacheable
import org.springframework.data.domain.Page
import org.springframework.data.domain.Pageable
import org.springframework.security.authentication.AuthenticationManager
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken
import org.springframework.security.core.userdetails.UserDetails
import org.springframework.security.core.userdetails.UserDetailsService
import org.springframework.security.core.userdetails.UsernameNotFoundException
import org.springframework.security.crypto.password.PasswordEncoder
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import java.time.LocalDateTime
import java.util.*

@Service
@Transactional
class UserService(
    private val userRepository: UserRepository,
    private val passwordEncoder: PasswordEncoder,
    private val jwtService: JwtService,
    private val authenticationManager: AuthenticationManager,
    private val emailService: EmailService
) : UserDetailsService {
    
    override fun loadUserByUsername(username: String): UserDetails {
        return userRepository.findByUsernameOrEmail(username, username)
            ?: throw UsernameNotFoundException("User not found: $username")
    }
    
    fun register(request: RegisterRequest): AuthResponse {
        if (userRepository.existsByEmail(request.email)) {
            throw EmailAlreadyExistsException("Email already registered")
        }
        
        if (userRepository.existsByUsername(request.username)) {
            throw EmailAlreadyExistsException("Username already taken")
        }
        
        val user = User(
            email = request.email,
            username = request.username,
            password = passwordEncoder.encode(request.password),
            firstName = request.firstName,
            lastName = request.lastName,
            role = UserRole.USER,
            enabled = true,
            emailVerified = false,
            emailVerificationToken = UUID.randomUUID().toString()
        )
        
        val savedUser = userRepository.save(user)
        
        // Send verification email
        emailService.sendVerificationEmail(savedUser)
        
        val token = jwtService.generateToken(savedUser)
        val refreshToken = jwtService.generateRefreshToken(savedUser)
        
        return AuthResponse(
            token = token,
            refreshToken = refreshToken,
            user = UserDto.fromEntity(savedUser)
        )
    }
    
    fun login(request: LoginRequest): AuthResponse {
        val authentication = authenticationManager.authenticate(
            UsernamePasswordAuthenticationToken(request.username, request.password)
        )
        
        val user = authentication.principal as User
        
        // Reset failed login attempts
        user.failedLoginAttempts = 0
        user.lastLoginAt = LocalDateTime.now()
        userRepository.save(user)
        
        val token = jwtService.generateToken(user)
        val refreshToken = jwtService.generateRefreshToken(user)
        
        return AuthResponse(
            token = token,
            refreshToken = refreshToken,
            user = UserDto.fromEntity(user)
        )
    }
    
    fun verifyEmail(token: String): Boolean {
        val user = userRepository.findByEmailVerificationToken(token)
            ?: throw InvalidTokenException("Invalid verification token")
        
        user.emailVerified = true
        user.emailVerificationToken = null
        userRepository.save(user)
        
        return true
    }
    
    fun forgotPassword(email: String) {
        val user = userRepository.findByEmail(email)
            ?: return // Don't reveal if email exists
        
        user.passwordResetToken = UUID.randomUUID().toString()
        user.passwordResetTokenExpiry = LocalDateTime.now().plusHours(24)
        userRepository.save(user)
        
        emailService.sendPasswordResetEmail(user)
    }
    
    fun resetPassword(token: String, newPassword: String): Boolean {
        val user = userRepository.findByPasswordResetToken(token)
            ?: throw InvalidTokenException("Invalid reset token")
        
        if (user.passwordResetTokenExpiry?.isBefore(LocalDateTime.now()) == true) {
            throw InvalidTokenException("Reset token expired")
        }
        
        user.setPassword(passwordEncoder.encode(newPassword))
        user.passwordResetToken = null
        user.passwordResetTokenExpiry = null
        userRepository.save(user)
        
        return true
    }
    
    @Cacheable("users")
    fun findById(id: UUID): User {
        return userRepository.findById(id)
            .orElseThrow { ResourceNotFoundException("User not found") }
    }
    
    @CacheEvict("users", key = "#id")
    fun updateUser(id: UUID, request: UpdateUserRequest): UserDto {
        val user = findById(id)
        
        request.firstName?.let { user.firstName = it }
        request.lastName?.let { user.lastName = it }
        
        return UserDto.fromEntity(userRepository.save(user))
    }
}