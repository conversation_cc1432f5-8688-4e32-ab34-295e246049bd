// dto/ContentDtos.kt
package com.jsmith.author.dto

import jakarta.validation.constraints.NotBlank
import jakarta.validation.constraints.Size
import java.time.LocalDate
import java.time.LocalDateTime
import java.util.UUID

// Novel DTOs
data class CreateNovelRequest(
    @field:NotBlank(message = "Title is required")
    @field:Size(max = 200)
    val title: String,
    
    @field:NotBlank(message = "Synopsis is required")
    val synopsis: String,
    
    @field:NotBlank(message = "Content is required")
    val content: String,
    
    val coverImage: String? = null,
    val publishedDate: LocalDate? = null,
    val isPublished: Boolean = false,
    val tags: Set<String> = emptySet(),
    val isbn: String? = null,
    val purchaseLink: String? = null
)

data class NovelDto(
    val id: UUID,
    val title: String,
    val slug: String,
    val synopsis: String,
    val content: String,
    val coverImage: String?,
    val publishedDate: LocalDate?,
    val viewCount: Long,
    val tags: Set<String>,
    val readingTime: Int?,
    val isbn: String?,
    val purchaseLink: String?,
    val commentCount: Int,
    val createdAt: LocalDateTime
) {
    companion object {
        fun fromEntity(novel: com.jsmith.author.entity.Novel): NovelDto {
            return NovelDto(
                id = novel.id,
                title = novel.title,
                slug = novel.slug,
                synopsis = novel.synopsis,
                content = novel.content,
                coverImage = novel.coverImage,
                publishedDate = novel.publishedDate,
                viewCount = novel.viewCount,
                tags = novel.tags,
                readingTime = novel.readingTime,
                isbn = novel.isbn,
                purchaseLink = novel.purchaseLink,
                commentCount = novel.comments.size,
                createdAt = novel.createdAt
            )
        }
    }
}

// Blog DTOs
data class CreateBlogRequest(
    @field:NotBlank(message = "Title is required")
    @field:Size(max = 200)
    val title: String,
    
    @field:NotBlank(message = "Excerpt is required")
    val excerpt: String,
    
    @field:NotBlank(message = "Content is required")
    val content: String,
    
    val featuredImage: String? = null,
    val publishedDate: LocalDate? = null,
    val isPublished: Boolean = false,
    val tags: Set<String> = emptySet(),
    val categories: Set<String> = emptySet()
)

data class BlogDto(
    val id: UUID,
    val title: String,
    val slug: String,
    val excerpt: String,
    val content: String,
    val featuredImage: String?,
    val publishedDate: LocalDate?,
    val viewCount: Long,
    val tags: Set<String>,
    val categories: Set<String>,
    val readingTime: Int?,
    val commentCount: Int,
    val createdAt: LocalDateTime
) {
    companion object {
        fun fromEntity(blog: com.jsmith.author.entity.Blog): BlogDto {
            return BlogDto(
                id = blog.id,
                title = blog.title,
                slug = blog.slug,
                excerpt = blog.excerpt,
                content = blog.content,
                featuredImage = blog.featuredImage,
                publishedDate = blog.publishedDate,
                viewCount = blog.viewCount,
                tags = blog.tags,
                categories = blog.categories,
                readingTime = blog.readingTime,
                commentCount = blog.comments.size,
                createdAt = blog.createdAt
            )
        }
    }
}

// Story DTOs
data class StoryDto(
    val id: UUID,
    val title: String,
    val slug: String,
    val synopsis: String,
    val content: String,
    val coverImage: String?,
    val publishedDate: LocalDate?,
    val viewCount: Long,
    val tags: Set<String>,
    val genre: String?,
    val readingTime: Int?,
    val commentCount: Int,
    val createdAt: LocalDateTime
) {
    companion object {
        fun fromEntity(story: com.jsmith.author.entity.Story): StoryDto {
            return StoryDto(
                id = story.id,
                title = story.title,
                slug = story.slug,
                synopsis = story.synopsis,
                content = story.content,
                coverImage = story.coverImage,
                publishedDate = story.publishedDate,
                viewCount = story.viewCount,
                tags = story.tags,
                genre = story.genre,
                readingTime = story.readingTime,
                commentCount = story.comments.size,
                createdAt = story.createdAt
            )
        }
    }
}

// Comment DTOs
data class CreateCommentRequest(
    @field:NotBlank(message = "Comment content is required")
    @field:Size(min = 1, max = 5000)
    val content: String,
    
    val novelId: UUID? = null,
    val blogId: UUID? = null,
    val storyId: UUID? = null,
    val parentId: UUID? = null
)

data class CommentDto(
    val id: UUID,
    val content: String,
    val author: String,
    val createdAt: LocalDateTime,
    val editedAt: LocalDateTime?,
    val replies: List<CommentDto>
) {
    companion object {
        fun fromEntity(comment: com.jsmith.author.entity.Comment): CommentDto {
            return CommentDto(
                id = comment.id,
                content = comment.content,
                author = comment.user.username,
                createdAt = comment.createdAt,
                editedAt = comment.editedAt,
                replies = comment.replies.map { fromEntity(it) }
            )
        }
    }
}

// Contact DTO
data class ContactRequest(
    @field:NotBlank(message = "Name is required")
    val name: String,
    
    @field:Email(message = "Invalid email format")
    @field:NotBlank(message = "Email is required")
    val email: String,
    
    val subject: String?,
    
    @field:NotBlank(message = "Message is required")
    @field:Size(min = 10, max = 5000)
    val message: String
)